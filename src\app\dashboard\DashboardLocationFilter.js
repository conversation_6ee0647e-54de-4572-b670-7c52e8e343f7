import React, { useState, useEffect } from "react";
import ReactSelect from 'react-select';
import { MultiSelect } from 'primereact/multiselect';
import { Button } from 'primereact/button';
import API from '../services/API';
import { PROJECTS_URL } from '../constants';
import moment from 'moment';

const DashboardLocationFilter = ({ onSelectionChange, dateRange, activeTab }) => {

    useEffect(() => { console.log(dateRange, activeTab, 'Date change or active tab change') }, [dateRange, activeTab]);

    const countries = [
        { name: 'UK', id: 'UK' },
        { name: 'India', id: 'IN' },
        { name: 'Singapore', id: 'SG' },
        { name: 'Thailand', id: 'TH' },
        { name: 'Indonesia', id: 'ID' },
        { name: 'Philippines', id: 'PH' },
        { name: 'Japan', id: 'JP' },
        { name: 'Korea', id: 'KR' }
    ];

    const buLevels = [
        { name: 'DC Ops', id: 'DC' },
        { name: 'Construction', id: 'Construction' },
        { name: 'Office', id: 'Office' }
    ];

    const [mergedSites, setMergedSites] = useState([]); // New state to hold merged sites
    const [isApiLoading, setIsApiLoading] = useState(true); // Loading state for API call


    const hardcodedSites = [
        { name: 'Project Noida 1 (NOD1)', id: 'Project_Noida_1_(NOD1)', country: 'IN', bu: 'Construction' },
        { name: 'Noida DC 1 (NOD1)', id: 'Noida_DC_1_(NOD1)', country: 'IN', bu: 'DC', uncheckDisable: true },
        { name: 'Pune DC 4 (PNQ23)', id: 'Pune_DC_4_(PNQ23)', country: 'IN', bu: 'Construction' },
        { name: 'Pune DC 5', id: 'Pune_DC_5', country: 'IN', bu: 'Construction' },
        { name: 'Pune DC 1 (PNQ1)', id: 'Pune_DC_1_(PNQ1)', country: 'IN', bu: 'DC', uncheckDisable: true },
        { name: 'Pune DC 2 (PNQ2)', id: 'Pune_DC_2_(PNQ2)', country: 'IN', bu: 'DC', uncheckDisable: true },
        { name: 'Pune DC 3 (PNQ3)', id: 'Pune_DC_3_(PNQ3)', country: 'IN', bu: 'DC', uncheckDisable: true },
        { name: 'Mumbai DC 4 (NMB1)', id: 'Mumbai_DC_4_(NMB1)', country: 'IN', bu: 'Construction' },
        { name: 'Mumbai DC 1 (MB1)', id: 'Mumbai_DC_1', country: 'IN', bu: 'Construction' },
        { name: 'Mumbai DC 2 (MB2)', id: 'Mumbai_DC_2', country: 'IN', bu: 'Construction' },
        { name: 'Mumbai DC 3 (MB3)', id: 'Mumbai_DC_3', country: 'IN', bu: 'Construction' },
        { name: 'Mumbai DC 1 (MB1)', id: 'Mumbai_DC_1_(MB1)', country: 'IN', bu: 'DC', uncheckDisable: true },
        { name: 'Mumbai DC 2 (MB2)', id: 'Mumbai_DC_2_(MB2)', country: 'IN', bu: 'DC', uncheckDisable: true },
        { name: 'Mumbai DC 3 (MB3)', id: 'Mumbai_DC_3_(MB3)', country: 'IN', bu: 'DC', uncheckDisable: true },
        { name: 'Kolkata DC 2 (KKT2)', id: 'Kolkata_DC_2_(KKT2)', country: 'IN', bu: 'Construction' },
        { name: 'Kolkata DC 1 (KKT1)', id: 'Kolkata_DC_1_(KKT1)', country: 'IN', bu: 'DC', uncheckDisable: true },
        { name: 'Bangalore DC 1 (BLR1)', id: 'Bangalore_DC_1_(BLR1)', country: 'IN', bu: 'DC', uncheckDisable: true },
        { name: 'Bangalore DC 2 (BLR2)', id: 'Bangalore_DC_2_(BLR2)', country: 'IN', bu: 'DC', uncheckDisable: true }, // Duplicate handled

        { name: 'Chennai 1 (CHN1)', id: 'Chennai_1_(CHN1)', country: 'IN', bu: 'DC', uncheckDisable: true },
        { name: 'Chennai 2 (CHN2)', id: 'Chennai_2_(CHN2)', country: 'IN', bu: 'DC', uncheckDisable: true },
        { name: 'Chennai 3 (CHN3)', id: 'Chennai_3_(CHN3)', country: 'IN', bu: 'DC', uncheckDisable: true },
        { name: 'Chennai DC 7', id: 'Chennai_DC_7', country: 'IN', bu: 'Construction' },
        { name: 'Hyderabad DC 1 (HYD1)', id: 'Hyderabad_DC_1_(HYD1)', country: 'IN', bu: 'DC', uncheckDisable: true },
        { name: 'Ahmedabad DC 1 (AMD1)', id: 'Ahmedabad_DC_1_(AMD1)', country: 'IN', bu: 'DC', uncheckDisable: true },
        { name: 'Delhi', id: 'Delhi', country: 'IN', bu: 'Construction' },
        { name: 'Delhi DC 1 (DEL1)', id: 'Delhi_DC_1_(DEL1)', country: 'IN', bu: 'DC', uncheckDisable: true },
        { name: 'Delhi DC 2 (DEL2)', id: 'Delhi_DC_2_(DEL2)', country: 'IN', bu: 'DC', uncheckDisable: true },
        { name: 'Delhi DC 3 (DEL3)', id: 'Delhi_DC_3_(DEL3)', country: 'IN', bu: 'DC', uncheckDisable: true },

        { name: 'Singapore Alfa', id: 'Singapore_Alfa', country: 'SG', bu: 'Construction' }, // Case-insensitive handling
        { name: 'Singapore LY Project Sunstone', id: 'SG_LY_Project_Sunstone', country: 'SG', bu: 'Construction' }, // Case-insensitive handling
        { name: 'Singapore MH Project E', id: 'SG_MH_Project_E', country: 'SG', bu: 'Construction' },
        { name: 'Singapore MH Project Sapphire', id: 'SG_MH_Project_Sapphire', country: 'SG', bu: 'Construction' },

        { name: 'Defu 1 (DF1)', id: 'Defu_1_(DF1)', country: 'SG', bu: 'DC', uncheckDisable: true },
        { name: 'Defu 2 (DF2)', id: 'Defu_2_(DF2)', country: 'SG', bu: 'DC', uncheckDisable: true },
        { name: 'Defu 3 (DF3)', id: 'Defu_3_(DF3)', country: 'SG', bu: 'DC', uncheckDisable: true },
        { name: 'Loyang (LY)', id: 'Loyang_(LY)', country: 'SG', bu: 'DC', uncheckDisable: true },
        { name: 'MediaHub (MH)', id: 'MediaHub_(MH)', country: 'SG', bu: 'DC', uncheckDisable: true },
        { name: 'TaiSeng (TS)', id: 'TaiSeng_(TS)', country: 'SG', bu: 'DC', uncheckDisable: true },
        { name: 'Terra Phase 2 (TER2)', id: 'Terra_Phase_2_(TER2)', country: 'TH', bu: 'Construction' },
        { name: 'Terra Phase 3 (TER3)', id: 'Terra_Phase_3_(TER3)', country: 'TH', bu: 'Construction' },
        { name: 'Terra Substation (TERS)', id: 'Terra_Substation_(TERS)', country: 'TH', bu: 'Construction' },
        { name: 'One Bangkok (OBK)', id: 'One_Bangkok_(OBK)', country: 'TH', bu: 'Construction' },
        { name: 'Bangkok 1 (BKK1)', id: 'Bangkok_1_(BKK1)', country: 'TH', bu: 'DC', uncheckDisable: true },
        { name: 'Bangkok (BKK3)', id: 'Bangkok_(BKK3)', country: 'TH', bu: 'DC', uncheckDisable: true },
        { name: 'Project JKT 1 (Phase 2)', id: 'Project_JKT_1_(Phase_2)', country: 'ID', bu: 'Construction' },
        { name: 'Jakarta 1 (JKT1)', id: 'Jakarta_1_(JKT1)', country: 'ID', bu: 'DC', uncheckDisable: true },
        { name: 'Mandaluyong 1 (MD1)', id: 'Mandaluyong_1_(MD1)', country: 'PH', bu: 'DC', uncheckDisable: true },
        { name: 'Makati 2 (MK2)', id: 'Makati_2_(MK2)', country: 'PH', bu: 'DC', uncheckDisable: true },
        { name: 'Quezon City 2 (QC2)', id: 'Quezon_City_2_(QC2)', country: 'PH', bu: 'DC', uncheckDisable: true },
        { name: 'Cavite City 1 (CAV1)', id: 'Cavite_City_1_(CAV1)', country: 'PH', bu: 'DC', uncheckDisable: true },
        { name: 'Davao 1 (DV1)', id: 'Davao_1_(DV1)', country: 'PH', bu: 'DC', uncheckDisable: true },
        { name: 'Polaris (PLR)', id: 'Polaris_(PLR)', country: 'PH', bu: 'Construction' },
        { name: 'Project Gamma', id: 'Project_Gamma', country: 'KR', bu: 'Construction' },
        { name: 'London 12 (LON12)', id: 'London_12_(LON12)', country: 'UK', bu: 'Construction', uncheckDisable: true },
        { name: 'London 14 (LON14)', id: 'London_14_(LON14)', country: 'UK', bu: 'Construction', uncheckDisable: true },

        { name: 'Project Chalk', id: 'Project_Chalk', country: 'UK', bu: 'Construction', uncheckDisable: true },
        { name: 'LONDON 7 (Stockley Park Campus)', id: 'LONDON_7_(Stockley_Park_Campus)', country: 'UK', bu: 'DC', uncheckDisable: true },

        { name: 'London 1 (LON1)', id: 'London_1_(LON1)', country: 'UK', bu: 'DC', uncheckDisable: true },
        { name: 'London 2 (LON2)', id: 'London_2_(LON2)', country: 'UK', bu: 'DC', uncheckDisable: true },
        { name: 'London 3 (LON3)', id: 'London_3_(LON3)', country: 'UK', bu: 'DC', uncheckDisable: true },
        { name: 'London 4 (LON4)', id: 'London_4_(LON4)', country: 'UK', bu: 'DC', uncheckDisable: true },
        { name: 'London 5 (LON5)', id: 'London_5_(LON5)', country: 'UK', bu: 'DC', uncheckDisable: true },
        { name: 'London 6 (LON6)', id: 'London_6_(LON6)', country: 'UK', bu: 'DC', uncheckDisable: true },
        { name: 'London 7 (LON7)', id: 'London_7_(LON7)', country: 'UK', bu: 'DC', uncheckDisable: true },
        { name: 'London 8 (LON8)', id: 'London_8_(LON8)', country: 'UK', bu: 'DC', uncheckDisable: true },
        { name: 'London 9 (LON9)', id: 'London_9_(LON9)', country: 'UK', bu: 'DC', uncheckDisable: true },
        { name: 'London 10 (LON10)', id: 'London_10_(LON10)', country: 'UK', bu: 'DC', uncheckDisable: true },
        { name: 'London 11 (LON11)', id: 'London_11_(LON11)', country: 'UK', bu: 'DC', uncheckDisable: true },
        { name: 'Jardin', id: 'JARDIN', country: 'JP', bu: 'Construction' },
        { name: 'Jaipur DC 1', id: 'Jaipur_DC_1', country: 'IN', bu: 'Construction' },

        { name: 'SG TS Ophir', id: 'SG_TS_Ophir', country: 'SG', bu: 'Construction' },
        { name: 'SG LY DS4-4 Alteration', id: 'SG_LY_DS4-4_Alteration', country: 'SG', bu: 'Construction' },
        { name: 'STT BKK 2', id: 'STT_BKK_2', country: 'TH', bu: 'Construction' },
        { name: 'IRIS Phase 2', id: 'IRIS_Phase_2', country: 'ID', bu: 'Construction' },
        { name: 'IRIS Phase 3', id: 'IRIS_Phase_3', country: 'ID', bu: 'Construction' },

        { name: 'Proxima', id: 'Proxima', country: 'PH', bu: 'Construction' },

        { name: 'DC OPS QC 2', id: 'DC_OPS_QC_2', country: 'PH', bu: 'DC' },
        { name: 'DC OPS Cavite', id: 'DC_OPS_Cavite', country: 'PH', bu: 'DC' },
        { name: 'DC OPS Davao', id: 'DC_OPS_Davao', country: 'PH', bu: 'DC' },
        { name: 'DC OPS Makati', id: 'DC_OPS_Makati', country: 'PH', bu: 'DC' },
        { name: 'DC OPS Mandaluyong', id: 'DC_OPS_Mandaluyong', country: 'PH', bu: 'DC' },


        {
            name: "One_Bangkok_(OBK)_(OBK)",
            id: "One_Bangkok_(OBK)_(OBK)",
            country: "TH",
            bu: "Construction"
        },
        {
            name: "Terra_Substation_(TERS)_(TERS)",
            id: "Terra_Substation_(TERS)_(TERS)",
            country: "TH",
            bu: "Construction"
        },
        {
            name: "Ophir",
            id: "Ophir",
            country: "SG",
            bu: "Construction"
        },
        {
            name: "Project_JKT_1_(Phase_3)",
            id: "Project_JKT_1_(Phase_3)",
            country: "ID",
            bu: "Construction"
        },
        {
            name: "Project_JKT_2",
            id: "Project_JKT_2",
            country: "ID",
            bu: "Construction"
        },
        {
            name: "STT_Jakarta_1",
            id: "STT_Jakarta_1",
            country: "ID",
            bu: "DC"
        },
        {
            name: "STT_Bangkok_1",
            id: "STT_Bangkok_1",
            country: "TH",
            bu: "DC"
        },
        {
            name: "STT_Singapore_6",
            id: "STT_Singapore_6",
            country: "SG",
            bu: "DC"
        },
        {
            name: "STT_Quezon_City",
            id: "STT_Quezon_City",
            country: "PH",
            bu: "DC"
        },
        {
            name: "STT_Mandaluyong",
            id: "STT_Mandaluyong",
            country: "PH",
            bu: "DC"
        },
        {
            name: "Project_BKK_2",
            id: "Project_BKK_2",
            country: "TH",
            bu: "Construction"
        },
        {
            name: "Project_Terra_Phase_4-5",
            id: "Project_Terra_Phase_4-5",
            country: "TH",
            bu: "Construction"
        },
        {
            name: "STT_Chennai_1",
            id: "STT_Chennai_1",
            country: "IN",
            bu: "DC"
        },
        {
            name: "STT_Chennai_2",
            id: "STT_Chennai_2",
            country: "IN",
            bu: "DC"
        },
        {
            name: "STT_Chennai_3",
            id: "STT_Chennai_3",
            country: "IN",
            bu: "DC"
        },
        {
            name: "STT_Bengaluru_1",
            id: "STT_Bengaluru_1",
            country: "IN",
            bu: "DC"
        },
        {
            name: "STT_Bengaluru_2",
            id: "STT_Bengaluru_2",
            country: "IN",
            bu: "DC"
        },
        {
            name: "STT_Bengaluru_3",
            id: "STT_Bengaluru_3",
            country: "IN",
            bu: "DC"
        },
        {
            name: "STT_Hyderabad_1",
            id: "STT_Hyderabad_1",
            country: "IN",
            bu: "DC"
        },
        {
            name: "STT_Kolkata_1",
            id: "STT_Kolkata_1",
            country: "IN",
            bu: "DC"
        },
        {
            name: "STT_Ahmedabad_1",
            id: "STT_Ahmedabad_1",
            country: "IN",
            bu: "DC"
        },
        {
            name: "STT_Mumbai_1",
            id: "STT_Mumbai_1",
            country: "IN",
            bu: "DC"
        },
        {
            name: "STT_Mumbai_2",
            id: "STT_Mumbai_2",
            country: "IN",
            bu: "DC"
        },
        {
            name: "STT_Mumbai_3",
            id: "STT_Mumbai_3",
            country: "IN",
            bu: "DC"
        },
        {
            name: "STT_NMDC_1",
            id: "STT_NMDC_1",
            country: "IN",
            bu: "DC"
        },
        {
            name: "STT_Pune_1",
            id: "STT_Pune_1",
            country: "IN",
            bu: "DC"
        },
        {
            name: "STT_Pune_2",
            id: "STT_Pune_2",
            country: "IN",
            bu: "DC"
        },
        {
            name: "STT_Pune_3",
            id: "STT_Pune_3",
            country: "IN",
            bu: "DC"
        },
        {
            name: "STT_Pune_4",
            id: "STT_Pune_4",
            country: "IN",
            bu: "DC"
        },
        {
            name: "STT_Noida_1",
            id: "STT_Noida_1",
            country: "IN",
            bu: "DC"
        },
        {
            name: "STT_Delhi_1",
            id: "STT_Delhi_1",
            country: "IN",
            bu: "DC"
        },
        {
            name: "STT_Delhi_2",
            id: "STT_Delhi_2",
            country: "IN",
            bu: "DC"
        },
        {
            name: "STT_Delhi_3",
            id: "STT_Delhi_3",
            country: "IN",
            bu: "DC"
        },
        {
            name: "Project_Chennai_DC_7",
            id: "Project_Chennai_DC_7",
            country: "IN",
            bu: "Construction"
        },
        {
            name: "Project_Pune_DC_5",
            id: "Project_Pune_DC_5",
            country: "IN",
            bu: "Construction"
        },
        {
            name: "STT_Bangkok_3",
            id: "STT_Bangkok_3",
            country: "TH",
            bu: "DC"
        },
        {
            name: "Project_Jaipur_DC_1",
            id: "Project_Jaipur_DC_1",
            country: "IN",
            bu: "Construction"
        },
        {
            name: "Project_London_14",
            id: "Project_London_14",
            country: "UK",
            bu: "Construction"
        },
        {
            name: "Project_Polaris",
            id: "Project_Polaris",
            country: "PH",
            bu: "Construction"
        },
        {
            name: "STT_Singapore_4",
            id: "STT_Singapore_4",
            country: "SG",
            bu: "DC"
        },
        {
            name: "Project_Jardin",
            id: "Project_Jardin",
            country: "JP",
            bu: "Construction"
        },
        {
            name: "Project_One_Bangkok",
            id: "Project_One_Bangkok",
            country: "TH",
            bu: "Construction"
        },
        {
            name: "Project_Terra_Substation",
            id: "Project_Terra_Substation",
            country: "TH",
            bu: "Construction"
        },
        {
            name: "Project_Kolkata_DC_2",
            id: "Project_Kolkata_DC_2",
            country: "IN",
            bu: "Construction"
        },
        {
            name: "Project_Navi_Mumbai_DC_1",
            id: "Project_Navi_Mumbai_DC_1",
            country: "IN",
            bu: "Construction"
        },
        {
            name: "Project_Navi_Mumbai_DC_2",
            id: "Project_Navi_Mumbai_DC_2",
            country: "IN",
            bu: "Construction"
        },
        {
            name: "Project_Navi_Mumbai_DC_3",
            id: "Project_Navi_Mumbai_DC_3",
            country: "IN",
            bu: "Construction"
        },
        {
            name: "STT_Cavite_1",
            id: "STT_Cavite_1",
            country: "PH",
            bu: "DC"
        },
        {
            name: "STT_Davao",
            id: "STT_Davao",
            country: "PH",
            bu: "DC"
        },
        {
            name: "STT_Singapore_5",
            id: "STT_Singapore_5",
            country: "SG",
            bu: "DC"
        },
        {
            name: "STT_Singapore_3",
            id: "STT_Singapore_3",
            country: "SG",
            bu: "DC"
        },
        {
            name: "STT_Singapore_2",
            id: "STT_Singapore_2",
            country: "SG",
            bu: "DC"
        },
        {
            name: "STT_Singapore_1",
            id: "STT_Singapore_1",
            country: "SG",
            bu: "DC"
        },
        {
            name: "STT_Makati",
            id: "STT_Makati",
            country: "PH",
            bu: "DC"
        },
        {
            name: "Project_Proxima",
            id: "Project_Proxima",
            country: "PH",
            bu: "Construction"
        }

        // Add more sites as necessary
    ];
    // Assume countries and other data structures are defined outside this component
    const [selectedCountry, setSelectedCountry] = useState([...countries]);
    const [selectedBuLevel, setSelectedBuLevel] = useState([...buLevels]);
    const [selectedSite, setSelectedSite] = useState([]);
    const [isInitialLoad, setIsInitialLoad] = useState(true);

    useEffect(() => {
        // Fetch projects from API and merge with hardcoded sites based on dateRange and activeTab
        const fetchProjects = async () => {
            try {
                setIsApiLoading(true);

                // Check if activeTab is 3 and dateRange exists
                if (activeTab === 3 && dateRange && dateRange.length === 2) {
                    // Parse years from dateRange
                    const startYear = moment(dateRange[0]).year();
                    const endYear = moment(dateRange[1]).year();

                    // Determine which sites to show based on year logic
                    if (endYear < 2025) {
                        // Only years below 2025 - show only hardcoded sites
                        setMergedSites(hardcodedSites);
                        setIsApiLoading(false);
                        return;
                    } else if (startYear >= 2025) {
                        // Only years 2025 and above - show only API sites
                        const response = await API.get(PROJECTS_URL);

                        if (response.status === 200 && response.data && response.data.length > 0) {
                            setMergedSites(response.data);
                        } else {
                            setMergedSites([]); // No fallback to hardcoded sites for 2025+
                        }
                        setIsApiLoading(false);
                        return;
                    } else {
                        // Mixed years (spans across 2025) - show both hardcoded and API sites
                        const response = await API.get(PROJECTS_URL);

                        if (response.status === 200 && response.data && response.data.length > 0) {
                            const apiSites = response.data;

                            // Remove duplicates by using a Set - merge hardcoded and API sites
                            const mergedList = [...hardcodedSites, ...apiSites].reduce((acc, site) => {
                                if (!acc.some(s => s.id === site.id)) acc.push(site);
                                return acc;
                            }, []);

                            setMergedSites(mergedList);
                        } else {
                            setMergedSites(hardcodedSites); // Fallback to hardcoded sites if no API data
                        }
                        setIsApiLoading(false);
                        return;
                    }
                } else {
                    // Default behavior for other tabs or when activeTab is not 3
                    const response = await API.get(PROJECTS_URL);

                    if (response.status === 200 && response.data && response.data.length > 0) {
                        const apiSites = response.data;

                        // Remove duplicates by using a Set - merge hardcoded and API sites
                        const mergedList = [...hardcodedSites, ...apiSites].reduce((acc, site) => {
                            if (!acc.some(s => s.id === site.id)) acc.push(site);
                            return acc;
                        }, []);

                        setMergedSites(mergedList);
                    } else {
                        setMergedSites(hardcodedSites); // Fallback to hardcoded sites if no API data
                    }
                }
            } catch (error) {
                console.error('Error fetching projects:', error);
                setMergedSites(hardcodedSites); // Fallback to hardcoded sites on error
            } finally {
                setIsApiLoading(false);
            }
        };

        fetchProjects();
    }, [dateRange, activeTab]);

    useEffect(() => {
        if (selectedCountry.length === 0) {
            setSelectedBuLevel([]);
            setSelectedSite([]);
        }
    }, [selectedCountry]);

    useEffect(() => {
        // Filter sites based on selected countries and BU Levels
        const countryIds = selectedCountry.map(country => country.id);
        const buIds = selectedBuLevel.map(bu => bu.id);

        if (buIds.length === 0) {
            setSelectedSite([]);
        } else {
            const filteredSites = mergedSites.filter(site =>
                countryIds.includes(site.country) &&
                buIds.includes(site.bu)
            );
            setSelectedSite(filteredSites);
        }
    }, [selectedCountry, selectedBuLevel, mergedSites]);

    // Apply filter automatically on initial load only - wait for API to complete
    useEffect(() => {
        if (isInitialLoad && !isApiLoading && mergedSites.length > 0 && selectedSite.length > 0) {
            // Calculate expected sites based on current country and BU selections
            const countryIds = selectedCountry.map(country => country.id);
            const buIds = selectedBuLevel.map(bu => bu.id);
            const expectedSites = mergedSites.filter(site =>
                countryIds.includes(site.country) &&
                buIds.includes(site.bu)
            );

            // Only trigger if all expected sites are selected
            if (selectedSite.length === expectedSites.length) {
                onSelectionChange({
                    countries: selectedCountry,
                    buLevels: selectedBuLevel,
                    sites: selectedSite
                });
                setIsInitialLoad(false);
            }
        }
    }, [selectedCountry, selectedBuLevel, selectedSite, mergedSites, isInitialLoad, isApiLoading, onSelectionChange]);

    // Apply Filter function
    const handleApplyFilter = () => {
        onSelectionChange({
            countries: selectedCountry,
            buLevels: selectedBuLevel,
            sites: selectedSite
        });
    };

    const handleCountryChange = (e) => {
        setSelectedCountry(e.value);
    };

    const handleBuLevelChange = (e) => {
        setSelectedBuLevel(e.value);
    };

    const handleSiteChange = (e) => {
        const alwaysSelectedCountries = selectedSite.filter(option => option.uncheckDisable);
        const newSelection = [...e.value, ...alwaysSelectedCountries];

        // Removing duplicates if any (optional, based on your logic)
        const uniqueNewSelection = Array.from(new Set(newSelection.map(a => a.id)))
            .map(id => {
                return newSelection.find(a => a.id === id)
            });
        setSelectedSite(uniqueNewSelection);
    };

    const generateSelectedItemsLabel = (selectedItems, totalItems, itemName) => {
        if (selectedItems.length === totalItems.length) {
            return `All ${itemName}`;
        } else {
            return `${selectedItems.length} ${itemName} selected`;
        }
    };


    return (
        <div className="container p-0">
            <div className="row">
                <div className="col-3">
                    <label>Country</label>
                    <MultiSelect
                        className="w-100"
                        value={selectedCountry}
                        options={countries}
                        onChange={handleCountryChange}
                        optionLabel="name"
                        placeholder="Select Countries"
                        maxSelectedLabels={3}
                        selectedItemsLabel={generateSelectedItemsLabel(selectedCountry, countries, "Countries")}
                    />
                </div>
                <div className="col-3">
                    <label>Business Unit</label>
                    <MultiSelect
                        className="w-100"
                        value={selectedBuLevel}
                        options={buLevels}
                        onChange={handleBuLevelChange}
                        optionLabel="name"
                        placeholder="Select BU"
                        maxSelectedLabels={2}
                        selectedItemsLabel={generateSelectedItemsLabel(selectedBuLevel, buLevels, "BU")}
                        disabled={!selectedCountry.length}
                    />
                </div>
                <div className="col-3">
                    <label>Site</label>
                    <MultiSelect
                        className="w-100"
                        value={selectedSite}
                        options={selectedBuLevel.length
                            ? mergedSites.filter(
                                (site) =>
                                    selectedCountry.map((c) => c.id).includes(site.country) &&
                                    selectedBuLevel.map((bu) => bu.id).includes(site.bu)
                            )
                            : []
                        }
                        onChange={handleSiteChange}
                        optionLabel="name"
                        placeholder="Select Site"
                        maxSelectedLabels={3}
                        selectedItemsLabel={generateSelectedItemsLabel(selectedSite, mergedSites, "Sites")}
                        disabled={!selectedBuLevel.length}
                    />
                </div>
                <div className="col-3 d-flex align-items-end">
                    <Button
                        label="Go"
                        icon="pi pi-filter"
                        onClick={handleApplyFilter}
                        className="w-100"
                        severity="primary"
                    />
                </div>
            </div>
        </div>
    );
};

export default DashboardLocationFilter;
